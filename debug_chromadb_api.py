#!/usr/bin/env python3
"""
Debug script to test ChromaDB API endpoints directly.
"""

import requests
import json

# ChromaDB server details
BASE_URL = "http://**************:8000"

def test_heartbeat():
    """Test the heartbeat endpoint."""
    print("Testing heartbeat...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/heartbeat", timeout=5)
        print(f"Heartbeat v1: Status {response.status_code}, Response: {response.text}")
    except Exception as e:
        print(f"Heartbeat v1 failed: {e}")

def test_list_collections():
    """Test listing collections."""
    print("\nTesting list collections...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/collections")
        print(f"List collections v1: Status {response.status_code}, Response: {response.text}")
    except Exception as e:
        print(f"List collections v1 failed: {e}")

def test_create_collection():
    """Test creating a collection."""
    print("\nTesting create collection...")
    
    # Test different payload formats
    payloads = [
        {"name": "test_collection", "metadata": {}},
        {"name": "test_collection", "metadata": {}, "get_or_create": True},
        {"name": "test_collection"},
    ]
    
    for i, payload in enumerate(payloads):
        print(f"\nTrying payload {i+1}: {payload}")
        try:
            response = requests.post(f"{BASE_URL}/api/v1/collections", json=payload)
            print(f"Create collection v1: Status {response.status_code}, Response: {response.text}")
            if response.status_code in [200, 201]:
                print("✅ Collection creation successful!")
                return True
        except Exception as e:
            print(f"Create collection v1 failed: {e}")
    
    return False

def test_add_documents():
    """Test adding documents to a collection."""
    print("\nTesting add documents...")
    
    # First ensure collection exists
    collection_name = "test_collection"
    
    # Test document payload
    payload = {
        "ids": ["test_id_1"],
        "embeddings": [[0.1, 0.2, 0.3, 0.4]],
        "metadatas": [{"source": "test"}],
        "documents": ["Test document content"]
    }
    
    print(f"Adding documents to collection: {collection_name}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/collections/{collection_name}/add", json=payload)
        print(f"Add documents v1: Status {response.status_code}, Response: {response.text}")
        if response.status_code == 200:
            print("✅ Document addition successful!")
            return True
    except Exception as e:
        print(f"Add documents v1 failed: {e}")
    
    return False

def test_query_collection():
    """Test querying a collection."""
    print("\nTesting query collection...")
    
    collection_name = "test_collection"
    payload = {
        "query_embeddings": [[0.1, 0.2, 0.3, 0.4]],
        "n_results": 1,
        "include": ["documents", "metadatas", "distances"]
    }
    
    print(f"Querying collection: {collection_name}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/collections/{collection_name}/query", json=payload)
        print(f"Query collection v1: Status {response.status_code}, Response: {response.text}")
        if response.status_code == 200:
            print("✅ Query successful!")
            return True
    except Exception as e:
        print(f"Query collection v1 failed: {e}")
    
    return False

if __name__ == "__main__":
    print("ChromaDB API Debug Test")
    print("=" * 50)
    
    # Test all endpoints
    test_heartbeat()
    test_list_collections()
    
    # Try to create collection
    collection_created = test_create_collection()
    
    if collection_created:
        # Test document operations
        test_add_documents()
        test_query_collection()
    else:
        print("\n❌ Could not create collection, skipping document tests")
    
    print("\n" + "=" * 50)
    print("Debug test completed")
