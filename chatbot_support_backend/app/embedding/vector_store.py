"""
Vector database management for storing and retrieving document embeddings.
"""

import os
import logging
import base64
import numpy as np
import uuid
import requests
import json
from typing import List, Dict, Any, Optional, Set

from langchain_community.vectorstores import FAISS
from langchain.schema.document import Document
from langchain.schema.embeddings import Embeddings
from sqlalchemy.orm import Session

from app.database.db import get_db
from app.database.models import Embedding as DBEmbedding, Document as DBDocument, DocumentAccess

logger = logging.getLogger(__name__)

class ExternalChromaClient:
    """
    Custom HTTP client for external ChromaDB that doesn't require importing chromadb library.
    """

    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.session = requests.Session()

    def heartbeat(self):
        """Check if the ChromaDB server is alive."""
        try:
            response = self.session.get(f"{self.base_url}/api/v2/heartbeat", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"ChromaDB heartbeat failed: {e}")
            return False

    def get_or_create_collection(self, name: str):
        """Get or create a collection."""
        try:
            # First, try to list all collections to see if it exists
            response = self.session.get(f"{self.base_url}/api/v2/collections")
            if response.status_code == 200:
                collections = response.json()
                # Check if our collection exists
                for collection in collections:
                    if collection.get('name') == name:
                        logger.info(f"Collection {name} already exists")
                        return collection

            # Collection doesn't exist, create it
            payload = {
                "name": name,
                "metadata": {},
                "get_or_create": True
            }
            response = self.session.post(f"{self.base_url}/api/v2/collections", json=payload)
            if response.status_code in [200, 201]:
                logger.info(f"Created collection {name}")
                return response.json()
            else:
                logger.error(f"Failed to create collection: Status {response.status_code}, Response: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error getting/creating collection: {e}")
            return None

    def add_documents(self, collection_name: str, documents: List[Document], embeddings: List[List[float]]):
        """Add documents to a collection."""
        try:
            # Prepare the payload
            ids = [str(uuid.uuid4()) for _ in documents]
            metadatas = [doc.metadata for doc in documents]
            documents_text = [doc.page_content for doc in documents]

            payload = {
                "ids": ids,
                "embeddings": embeddings,
                "metadatas": metadatas,
                "documents": documents_text
            }

            response = self.session.post(
                f"{self.base_url}/api/v2/collections/{collection_name}/add",
                json=payload
            )

            if response.status_code == 200:
                return True
            else:
                logger.error(f"Failed to add documents: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return False

    def query(self, collection_name: str, query_embeddings: List[List[float]], n_results: int = 4):
        """Query the collection for similar documents."""
        try:
            payload = {
                "query_embeddings": query_embeddings,
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }

            response = self.session.post(
                f"{self.base_url}/api/v2/collections/{collection_name}/query",
                json=payload
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to query collection: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error querying collection: {e}")
            return None


class ExternalChromaVectorStore:
    """
    Wrapper class that provides langchain-compatible interface for external ChromaDB.
    This class implements the methods expected by the retriever (as_retriever, similarity_search, etc.)
    """

    def __init__(self, client: ExternalChromaClient, collection_name: str, embeddings):
        self.client = client
        self.collection_name = collection_name
        self.embeddings = embeddings

    def similarity_search(self, query: str, k: int = 4, **kwargs) -> List[Document]:
        """Perform similarity search and return Document objects."""
        try:
            # Generate query embedding
            query_embedding = self.embeddings.embed_query(query)

            # Query the external ChromaDB
            results = self.client.query(
                self.collection_name,
                [query_embedding],
                n_results=k
            )

            if not results:
                logger.warning("No results from external ChromaDB query")
                return []

            # Convert results to Document objects
            documents = []
            if 'documents' in results and results['documents']:
                for i, doc_text in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if 'metadatas' in results and results['metadatas'] else {}

                    doc = Document(
                        page_content=doc_text,
                        metadata=metadata
                    )
                    documents.append(doc)

            return documents
        except Exception as e:
            logger.error(f"Error in similarity_search: {e}")
            return []

    def similarity_search_with_score(self, query: str, k: int = 4, **kwargs) -> List[tuple]:
        """Perform similarity search and return (Document, score) tuples."""
        try:
            # Generate query embedding
            query_embedding = self.embeddings.embed_query(query)

            # Query the external ChromaDB
            results = self.client.query(
                self.collection_name,
                [query_embedding],
                n_results=k
            )

            if not results:
                logger.warning("No results from external ChromaDB query")
                return []

            # Convert results to (Document, score) tuples
            documents_with_scores = []
            if 'documents' in results and results['documents']:
                for i, doc_text in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if 'metadatas' in results and results['metadatas'] else {}
                    distance = results['distances'][0][i] if 'distances' in results and results['distances'] else 0.0

                    # Convert distance to similarity score (lower distance = higher similarity)
                    score = 1.0 - distance if distance <= 1.0 else 0.0

                    doc = Document(
                        page_content=doc_text,
                        metadata=metadata
                    )
                    documents_with_scores.append((doc, score))

            return documents_with_scores
        except Exception as e:
            logger.error(f"Error in similarity_search_with_score: {e}")
            return []

    def as_retriever(self, **kwargs):
        """Return a retriever interface compatible with langchain."""

        class ExternalChromaRetriever:
            def __init__(self, vector_store):
                self.vector_store = vector_store

            def get_relevant_documents(self, query: str, **kwargs) -> List[Document]:
                k = kwargs.get('k', 4)
                return self.vector_store.similarity_search(query, k=k)

            def _get_relevant_documents(self, query: str, **kwargs) -> List[Document]:
                # For compatibility with BaseRetriever interface
                return self.get_relevant_documents(query, **kwargs)

        return ExternalChromaRetriever(self)

    def add_documents(self, documents: List[Document], **kwargs):
        """Add documents to the external ChromaDB."""
        try:
            # Generate embeddings for all documents
            embeddings_list = []
            for doc in documents:
                embedding = self.embeddings.embed_query(doc.page_content)
                embeddings_list.append(embedding)

            # Add documents to the collection
            success = self.client.add_documents(
                self.collection_name,
                documents,
                embeddings_list
            )

            if not success:
                raise RuntimeError("Failed to add documents to external ChromaDB")

            return True
        except Exception as e:
            logger.error(f"Error adding documents to external ChromaDB: {e}")
            raise


def get_chroma_client_settings():
    """
    Get ChromaDB client settings based on environment variables.

    Returns:
        dict: ChromaDB client settings
    """
    use_external = os.getenv("USE_EXTERNAL_CHROMA", "false").lower() == "true"

    if use_external:
        host = os.getenv("CHROMA_HOST", "localhost")
        port = int(os.getenv("CHROMA_PORT", "8000"))

        logger.info(f"Using external ChromaDB at {host}:{port}")

        # Create custom HTTP client for external ChromaDB server
        client = ExternalChromaClient(host, port)

        # Test connection
        if not client.heartbeat():
            logger.error(f"Cannot connect to external ChromaDB at {host}:{port}")
            raise ConnectionError(f"Cannot connect to external ChromaDB at {host}:{port}")

        return {
            "client": client,
            "collection_name": os.getenv("CHROMA_COLLECTION_NAME", "chatbot_documents"),
            "persist_directory": None,  # Not used for external ChromaDB
            "is_external": True
        }
    else:
        logger.info("Using local ChromaDB with file persistence")
        return {
            "client": None,  # Will use default local client
            "collection_name": None,  # Will use default collection
            "persist_directory": os.getenv("VECTOR_STORE_DIR", "/app/central_vector_store"),
            "is_external": False
        }

def custom_filter_complex_metadata(metadata: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter complex metadata to only include simple types that Chroma can handle.

    Args:
        metadata: The metadata dictionary to filter

    Returns:
        A new metadata dictionary with only simple types
    """
    if not isinstance(metadata, dict):
        logger.warning(f"Metadata is not a dictionary: {type(metadata)}")
        return {}

    filtered_metadata = {}
    for key, value in metadata.items():
        # Only include simple types: str, int, float, bool
        if isinstance(value, (str, int, float, bool)):
            filtered_metadata[key] = value
        elif value is None:
            # Skip None values
            continue
        elif isinstance(value, list):
            # For lists, convert to a string representation if possible
            try:
                # Join strings with commas
                if all(isinstance(item, str) for item in value):
                    filtered_metadata[key] = ",".join(value)
                # For lists of simple types, convert to a string
                else:
                    filtered_metadata[key] = str(value)
            except Exception as e:
                logger.warning(f"Could not convert list to string for key {key}: {str(e)}")
        else:
            # For other complex types, convert to a string representation
            try:
                filtered_metadata[key] = str(value)
            except Exception as e:
                logger.warning(f"Could not convert value to string for key {key}: {str(e)}")

    return filtered_metadata

class VectorStore:
    """
    Manages the storage and retrieval of document embeddings.
    """

    def __init__(
        self,
        embeddings: Embeddings,
        store_type: str = "db",
        persist_directory: Optional[str] = None,
        user_id: Optional[str] = None
    ):
        """
        Initialize the vector store.

        Args:
            embeddings: The embeddings instance to use
            store_type: Type of vector store ('db', 'chroma', or 'faiss')
            persist_directory: Directory to persist the vector store (for Chroma)
            user_id: The ID of the user who owns this vector store
        """
        self.embeddings = embeddings
        self.store_type = store_type.lower()
        self.user_id = user_id  # Store user_id for access control filtering

        # Get ChromaDB settings
        if self.store_type == "chroma":
            self.chroma_settings = get_chroma_client_settings()
            self.persist_directory = self.chroma_settings["persist_directory"] or persist_directory
            self.chroma_client = self.chroma_settings["client"]
            self.collection_name = self.chroma_settings["collection_name"]
            self.is_external_chroma = self.chroma_settings.get("is_external", False)
        else:
            self.persist_directory = persist_directory
            self.chroma_client = None
            self.collection_name = None
            self.is_external_chroma = False

        self.vector_store = None

        logger.info(f"Initializing {store_type} vector store")

    def create_from_documents(self, documents: List[Document]) -> None:
        """
        Create a vector store from a list of documents.

        Args:
            documents: List of Document objects
        """
        try:
            if self.store_type == "db":
                # For database storage, we'll just add the documents
                self.add_documents(documents)
                logger.info(f"Created database vector store with {len(documents)} documents")
            elif self.store_type == "faiss":
                self.vector_store = FAISS.from_documents(documents, self.embeddings)
                logger.info(f"Created FAISS vector store with {len(documents)} documents")
            elif self.store_type == "chroma":
                # Filter complex metadata before creating the vector store
                filtered_documents = []
                for doc in documents:
                    # Create a copy of the document with filtered metadata
                    filtered_doc = Document(
                        page_content=doc.page_content,
                        metadata=custom_filter_complex_metadata(doc.metadata)
                    )
                    filtered_documents.append(filtered_doc)

                logger.info(f"Filtered complex metadata from {len(documents)} documents")

                # Create ChromaDB vector store based on configuration
                if self.is_external_chroma:
                    # External ChromaDB using custom HTTP client
                    logger.info(f"Creating external ChromaDB vector store with collection: {self.collection_name}")

                    # Create or get collection
                    collection = self.chroma_client.get_or_create_collection(self.collection_name)
                    if not collection:
                        raise RuntimeError(f"Failed to create/get collection: {self.collection_name}")

                    # Create the wrapper that provides langchain-compatible interface
                    self.vector_store = ExternalChromaVectorStore(
                        client=self.chroma_client,
                        collection_name=self.collection_name,
                        embeddings=self.embeddings
                    )

                    # Add documents using the wrapper
                    self.vector_store.add_documents(filtered_documents)

                else:
                    # Local ChromaDB with file persistence - import Chroma only when needed
                    from langchain_community.vectorstores import Chroma

                    if self.persist_directory:
                        import os
                        os.makedirs(self.persist_directory, exist_ok=True)
                        # Set permissions to ensure the directory is writable
                        try:
                            os.chmod(self.persist_directory, 0o777)
                            logger.info(f"Set permissions on directory: {self.persist_directory}")
                        except Exception as e:
                            logger.warning(f"Could not set permissions on directory {self.persist_directory}: {str(e)}")

                    self.vector_store = Chroma.from_documents(
                        documents=filtered_documents,
                        embedding=self.embeddings,
                        persist_directory=self.persist_directory
                    )
                    if self.persist_directory:
                        self.vector_store.persist()

                logger.info(f"Created Chroma vector store with {len(documents)} documents")
            else:
                raise ValueError(f"Unsupported vector store type: {self.store_type}")
        except Exception as e:
            logger.error(f"Error creating vector store: {str(e)}")
            raise

    def add_documents(self, documents: List[Document]) -> None:
        """
        Add documents to an existing vector store.

        Args:
            documents: List of Document objects
        """
        try:
            if self.store_type == "db":
                # Store embeddings in the database
                with get_db() as db:
                    for i, doc in enumerate(documents):
                        # Get document metadata
                        doc_id = doc.metadata.get('document_id')

                        if not doc_id:
                            # If no document_id in metadata, try to find the document in the database
                            filename = doc.metadata.get('file_name', '')
                            if filename:
                                logger.info(f"Looking up document ID for filename: {filename}")
                                db_doc = db.query(DBDocument).filter(DBDocument.filename == filename).first()

                                if db_doc:
                                    doc_id = db_doc.id
                                    logger.info(f"Found document ID {doc_id} for filename {filename}")
                                else:
                                    logger.warning(f"No document found in database for filename: {filename}")
                            else:
                                logger.warning(f"No filename in metadata for document chunk: {doc.page_content[:50]}...")

                            if not doc_id:
                                # Skip if we can't associate with a document
                                logger.warning(f"Skipping document chunk without document_id: {doc.page_content[:50]}...")
                                continue

                        try:
                            # Generate embedding
                            embedding_vector = self.embeddings.embed_query(doc.page_content)

                            # Convert numpy array to base64 string for storage
                            vector_bytes = np.array(embedding_vector, dtype=np.float32).tobytes()
                            vector_b64 = base64.b64encode(vector_bytes).decode('utf-8')

                            # Check if this chunk already exists in the database
                            existing_embedding = db.query(DBEmbedding).filter(
                                DBEmbedding.document_id == doc_id,
                                DBEmbedding.chunk_text == doc.page_content
                            ).first()

                            if existing_embedding:
                                logger.info(f"Chunk already exists in database for document {doc_id}, updating embedding")
                                existing_embedding.embedding_vector = vector_b64
                                existing_embedding.chunk_index = i
                            else:
                                # Create embedding record
                                db_embedding = DBEmbedding(
                                    id=str(uuid.uuid4()),
                                    document_id=doc_id,
                                    chunk_index=i,
                                    chunk_text=doc.page_content,
                                    embedding_vector=vector_b64
                                )
                                db.add(db_embedding)
                                logger.info(f"Added new embedding for document {doc_id}, chunk {i}")
                        except Exception as chunk_error:
                            logger.error(f"Error processing chunk {i} for document {doc_id}: {str(chunk_error)}")

                        # Alternatively, use the vector property
                        # db_embedding = DBEmbedding(
                        #     id=str(uuid.uuid4()),
                        #     document_id=doc_id,
                        #     chunk_index=i,
                        #     chunk_text=doc.page_content
                        # )
                        # db_embedding.vector = np.array(embedding_vector, dtype=np.float32)

                    # Commit all changes at once
                    db.commit()
                    logger.info(f"Committed {len(documents)} document embeddings to database")
                logger.info(f"Added {len(documents)} document embeddings to database")
            elif self.vector_store:
                if self.store_type == "chroma":
                    # Filter complex metadata before adding to Chroma
                    filtered_documents = []
                    for doc in documents:
                        # Create a copy of the document with filtered metadata
                        filtered_doc = Document(
                            page_content=doc.page_content,
                            metadata=custom_filter_complex_metadata(doc.metadata)
                        )
                        filtered_documents.append(filtered_doc)

                    logger.info(f"Filtered complex metadata from {len(documents)} documents")

                    if self.is_external_chroma:
                        # External ChromaDB using wrapper
                        self.vector_store.add_documents(filtered_documents)
                    else:
                        # Local ChromaDB
                        self.vector_store.add_documents(filtered_documents)
                else:
                    # For other vector stores
                    self.vector_store.add_documents(documents)

                # Persist if needed (only for local ChromaDB)
                if self.store_type == "chroma" and self.persist_directory and not self.is_external_chroma:
                    self.vector_store.persist()

                logger.info(f"Added {len(documents)} documents to vector store")
            else:
                # Initialize vector store if needed
                self.create_from_documents(documents)
        except Exception as e:
            logger.error(f"Error adding documents to vector store: {str(e)}")
            raise

    def similarity_search(self, query: str, k: int = 4, user_id: Optional[str] = None, accessible_docs: Optional[Set[str]] = None) -> List[Document]:
        """
        Perform a similarity search for a query.

        Args:
            query: The query text
            k: Number of results to return
            user_id: Optional user ID for access control filtering
            accessible_docs: Optional set of document IDs the user has access to

        Returns:
            List of Document objects
        """
        try:
            if self.store_type == "db":
                # Generate query embedding
                query_embedding = self.embeddings.embed_query(query)
                query_vector = np.array(query_embedding, dtype=np.float32)

                # Retrieve similar documents from database
                with get_db() as db:
                    # Get embeddings with access control if user_id is provided
                    if user_id and not accessible_docs:
                        # Get documents the user has access to
                        from app.auth.user import UserManager
                        user_manager = UserManager()
                        accessible_docs = user_manager.get_accessible_documents(user_id)
                        logger.info(f"User {user_id} has access to {len(accessible_docs)} documents")

                    # Apply access control filter if accessible_docs is provided
                    if accessible_docs:
                        embeddings = db.query(DBEmbedding).filter(DBEmbedding.document_id.in_(accessible_docs)).all()
                        logger.info(f"Filtered to {len(embeddings)} embeddings based on document access")
                    else:
                        # Get all embeddings if no access control
                        embeddings = db.query(DBEmbedding).all()
                        logger.info(f"Using all {len(embeddings)} embeddings (no access control)")

                    # Calculate similarity scores
                    results_with_scores = []
                    for db_embedding in embeddings:
                        # Get document embedding vector
                        doc_vector = db_embedding.vector

                        # Calculate cosine similarity
                        similarity = np.dot(query_vector, doc_vector) / (np.linalg.norm(query_vector) * np.linalg.norm(doc_vector))

                        # Add to results
                        results_with_scores.append((db_embedding, similarity))

                    # Sort by similarity score (descending)
                    results_with_scores.sort(key=lambda x: x[1], reverse=True)

                    # Take top k results
                    top_results = results_with_scores[:k]

                    # Convert to Document objects
                    documents = []
                    for db_embedding, score in top_results:
                        # Get associated document
                        db_doc = db.query(DBDocument).filter(DBDocument.id == db_embedding.document_id).first()

                        if db_doc:
                            # Create Document object
                            doc = Document(
                                page_content=db_embedding.chunk_text,
                                metadata={
                                    'document_id': db_doc.id,
                                    'file_name': db_doc.filename,
                                    's3_key': db_doc.s3_key,
                                    'score': score,
                                    'chunk_index': db_embedding.chunk_index
                                }
                            )
                            documents.append(doc)

                    logger.info(f"Retrieved {len(documents)} documents from database for query: {query}")
                    return documents
            elif self.vector_store:
                if self.store_type == "chroma":
                    if self.is_external_chroma:
                        # External ChromaDB using custom HTTP client
                        query_embedding = self.embeddings.embed_query(query)

                        # Query the external ChromaDB
                        results = self.chroma_client.query(
                            self.collection_name,
                            [query_embedding],
                            n_results=k*3 if (user_id or accessible_docs) else k
                        )

                        if not results:
                            logger.warning("No results from external ChromaDB query")
                            return []

                        # Convert results to Document objects
                        documents = []
                        if 'documents' in results and results['documents']:
                            for i, doc_text in enumerate(results['documents'][0]):
                                metadata = results['metadatas'][0][i] if 'metadatas' in results and results['metadatas'] else {}
                                distance = results['distances'][0][i] if 'distances' in results and results['distances'] else 0.0

                                # Add similarity score (convert distance to similarity)
                                metadata['score'] = 1.0 - distance if distance <= 1.0 else 0.0

                                doc = Document(
                                    page_content=doc_text,
                                    metadata=metadata
                                )
                                documents.append(doc)

                        # Apply access control filtering if needed
                        if user_id or accessible_docs:
                            # Get documents the user has access to if not provided
                            if user_id and not accessible_docs:
                                from app.auth.user import UserManager
                                user_manager = UserManager()
                                accessible_docs = user_manager.get_accessible_documents(user_id)
                                logger.info(f"User {user_id} has access to {len(accessible_docs)} documents")

                            # Filter results based on document access
                            if accessible_docs:
                                filtered_results = [
                                    doc for doc in documents
                                    if doc.metadata.get('document_id') in accessible_docs
                                ]
                                logger.info(f"Filtered from {len(documents)} to {len(filtered_results)} results based on document access")
                                # Return only up to k results
                                return filtered_results[:k]

                        logger.info(f"Retrieved {len(documents)} documents from external ChromaDB for query: {query}")
                        return documents

                    elif (user_id or accessible_docs):
                        # Local Chroma with access control
                        # First, get more results than needed to account for filtering
                        all_results = self.vector_store.similarity_search(query, k=k*3)

                        # Get documents the user has access to if not provided
                        if user_id and not accessible_docs:
                            from app.auth.user import UserManager
                            user_manager = UserManager()
                            accessible_docs = user_manager.get_accessible_documents(user_id)
                            logger.info(f"User {user_id} has access to {len(accessible_docs)} documents")

                        # Filter results based on document access
                        if accessible_docs:
                            filtered_results = [
                                doc for doc in all_results
                                if doc.metadata.get('document_id') in accessible_docs
                            ]
                            logger.info(f"Filtered from {len(all_results)} to {len(filtered_results)} results based on document access")
                            # Return only up to k results
                            return filtered_results[:k]

                # Default behavior without access control
                results = self.vector_store.similarity_search(query, k=k)
                logger.info(f"Retrieved {len(results)} documents for query: {query}")
                return results
            else:
                logger.warning("Vector store not initialized. Returning empty results.")
                return []
        except Exception as e:
            logger.error(f"Error performing similarity search: {str(e)}")
            raise

    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document from the vector store.

        Args:
            document_id: The ID of the document to delete

        Returns:
            True if the document was deleted, False otherwise
        """
        try:
            if self.store_type == "db":
                # For database storage, delete embeddings from the database
                with get_db() as db:
                    # Delete all embeddings for this document
                    count = db.query(DBEmbedding).filter(DBEmbedding.document_id == document_id).count()
                    db.query(DBEmbedding).filter(DBEmbedding.document_id == document_id).delete()
                    db.commit()
                    logger.info(f"Deleted {count} embeddings for document {document_id} from database")
                return True
            elif self.vector_store:
                if self.store_type == "chroma":
                    # For Chroma, we can delete by metadata filter
                    try:
                        # Try to delete by document_id in metadata
                        self.vector_store.delete(
                            where={"document_id": document_id}
                        )
                        # Persist changes
                        if self.persist_directory:
                            self.vector_store.persist()
                        logger.info(f"Deleted document {document_id} from Chroma vector store")
                        return True
                    except Exception as e:
                        logger.error(f"Error deleting document from Chroma vector store: {str(e)}")
                        return False
                elif self.store_type == "faiss":
                    # FAISS doesn't support selective deletion
                    # We would need to rebuild the index without the document
                    logger.warning(f"Selective deletion not supported for FAISS vector store. Document {document_id} not deleted.")
                    return False
                else:
                    logger.warning(f"Deletion not supported for vector store type: {self.store_type}")
                    return False
            else:
                logger.warning("Vector store not initialized. Nothing to delete.")
                return False
        except Exception as e:
            logger.error(f"Error deleting document from vector store: {str(e)}")
            return False

    def reset(self) -> bool:
        """
        Completely reset the vector store, removing all documents and embeddings.
        This is useful when the vector store is in an inconsistent state.

        Returns:
            True if the reset was successful, False otherwise
        """
        try:
            if self.store_type == "db":
                # For database storage, delete all embeddings from the database
                with get_db() as db:
                    # Delete all embeddings
                    count = db.query(DBEmbedding).count()
                    db.query(DBEmbedding).delete()
                    db.commit()
                    logger.info(f"Deleted all {count} embeddings from database")
                return True
            elif self.store_type == "chroma":
                # For Chroma, we need to completely recreate the vector store
                try:
                    # First, try to delete all documents
                    try:
                        self.vector_store.delete(where={})
                        logger.info("Deleted all documents from Chroma vector store")
                    except Exception as e:
                        logger.warning(f"Error deleting all documents from Chroma: {str(e)}")

                    # Handle reset based on ChromaDB configuration
                    if self.chroma_client:
                        # External ChromaDB - just recreate the vector store connection
                        try:
                            from langchain_community.vectorstores import Chroma
                            self.vector_store = Chroma(
                                client=self.chroma_client,
                                collection_name=self.collection_name,
                                embedding_function=self.embeddings
                            )
                            logger.info(f"Reset external Chroma vector store with collection: {self.collection_name}")
                            return True
                        except Exception as e:
                            logger.error(f"Error resetting external Chroma vector store: {str(e)}")
                            return False
                    elif self.persist_directory:
                        # Local ChromaDB with file persistence
                        import shutil
                        import os

                        # Close the current vector store
                        try:
                            if hasattr(self.vector_store._client, "close"):
                                self.vector_store._client.close()
                            logger.info("Closed Chroma client")
                        except Exception as e:
                            logger.warning(f"Error closing Chroma client: {str(e)}")

                        # Delete the persist directory
                        try:
                            if os.path.exists(self.persist_directory):
                                shutil.rmtree(self.persist_directory)
                                logger.info(f"Deleted Chroma persist directory: {self.persist_directory}")
                        except Exception as e:
                            logger.error(f"Error deleting Chroma persist directory: {str(e)}")
                            return False

                        # Create a new vector store
                        try:
                            from langchain_community.vectorstores import Chroma

                            # Ensure the persist directory exists and has correct permissions
                            os.makedirs(self.persist_directory, exist_ok=True)
                            # Set permissions to ensure the directory is writable
                            try:
                                os.chmod(self.persist_directory, 0o777)
                                logger.info(f"Set permissions on directory: {self.persist_directory}")
                            except Exception as e:
                                logger.warning(f"Could not set permissions on directory {self.persist_directory}: {str(e)}")

                            self.vector_store = Chroma(
                                embedding_function=self.embeddings,
                                persist_directory=self.persist_directory
                            )
                            logger.info(f"Created new local Chroma vector store at {self.persist_directory}")
                            return True
                        except Exception as e:
                            logger.error(f"Error creating new local Chroma vector store: {str(e)}")
                            return False
                    else:
                        # In-memory ChromaDB
                        try:
                            from langchain_community.vectorstores import Chroma
                            self.vector_store = Chroma(
                                embedding_function=self.embeddings
                            )
                            logger.info("Created new in-memory Chroma vector store")
                            return True
                        except Exception as e:
                            logger.error(f"Error creating new in-memory Chroma vector store: {str(e)}")
                            return False
                except Exception as e:
                    logger.error(f"Error resetting Chroma vector store: {str(e)}")
                    return False
            elif self.store_type == "faiss":
                # For FAISS, create a new empty index
                try:
                    from langchain_community.vectorstores import FAISS
                    self.vector_store = FAISS.from_texts(
                        ["placeholder"],
                        self.embeddings
                    )
                    # Delete the placeholder
                    self.vector_store.delete([0])
                    logger.info("Reset FAISS vector store")
                    return True
                except Exception as e:
                    logger.error(f"Error resetting FAISS vector store: {str(e)}")
                    return False
            else:
                logger.warning(f"Reset not supported for vector store type: {self.store_type}")
                return False
        except Exception as e:
            logger.error(f"Error resetting vector store: {str(e)}")
            return False

    def save(self, save_path: str) -> None:
        """
        Save the vector store to disk.

        Args:
            save_path: Path to save the vector store
        """
        try:
            if self.store_type == "db":
                # No need to save for database storage - it's already persisted
                logger.info("Database vector store is already persisted")
                return
            elif self.vector_store:
                if self.store_type == "faiss":
                    self.vector_store.save_local(save_path)
                    logger.info(f"Saved FAISS vector store to {save_path}")
                elif self.store_type == "chroma":
                    if self.persist_directory and not self.chroma_client:
                        # Only persist for local ChromaDB
                        self.vector_store.persist()
                        logger.info(f"Persisted local Chroma vector store to {self.persist_directory}")
                    elif self.chroma_client:
                        # External ChromaDB is automatically persisted
                        logger.info("External Chroma vector store is automatically persisted")
                    else:
                        logger.warning("In-memory Chroma vector store cannot be persisted")
                else:
                    logger.warning("Save operation not supported for this vector store configuration")
            else:
                logger.warning("Vector store not initialized. Nothing to save.")
        except Exception as e:
            logger.error(f"Error saving vector store: {str(e)}")
            raise

    @classmethod
    def load(
        cls,
        load_path: str,
        embeddings: Embeddings,
        store_type: str = "db",
        user_id: Optional[str] = None
    ) -> 'VectorStore':
        """
        Load a vector store from disk or initialize from database.

        Args:
            load_path: Path to load the vector store from (for file-based stores)
            embeddings: The embeddings instance to use
            store_type: Type of vector store ('db', 'chroma', or 'faiss')
            user_id: The ID of the user who owns this vector store

        Returns:
            Initialized VectorStore instance
        """
        instance = cls(
            embeddings=embeddings,
            store_type=store_type,
            persist_directory=load_path if store_type == "chroma" else None,
            user_id=user_id
        )

        try:
            if store_type == "db":
                # For database storage, we don't need to load anything
                # The database is already persistent
                logger.info("Initialized database vector store")
                return instance
            elif store_type == "faiss":
                instance.vector_store = FAISS.load_local(load_path, embeddings)
                logger.info(f"Loaded FAISS vector store from {load_path}")
            elif store_type == "chroma":
                # Load ChromaDB based on configuration
                if instance.is_external_chroma:
                    # External ChromaDB using custom HTTP client
                    logger.info(f"Loading external ChromaDB vector store with collection: {instance.collection_name}")

                    # Create or get collection to ensure it exists
                    collection = instance.chroma_client.get_or_create_collection(instance.collection_name)
                    if not collection:
                        logger.warning(f"Could not get/create collection: {instance.collection_name}")

                    # Create the wrapper that provides langchain-compatible interface
                    instance.vector_store = ExternalChromaVectorStore(
                        client=instance.chroma_client,
                        collection_name=instance.collection_name,
                        embeddings=embeddings
                    )
                    logger.info(f"Loaded external Chroma vector store")
                else:
                    # Local ChromaDB with file persistence - import Chroma only when needed
                    from langchain_community.vectorstores import Chroma

                    if load_path:
                        import os
                        os.makedirs(load_path, exist_ok=True)
                        # Set permissions to ensure the directory is writable
                        try:
                            os.chmod(load_path, 0o777)
                            logger.info(f"Set permissions on directory: {load_path}")
                        except Exception as e:
                            logger.warning(f"Could not set permissions on directory {load_path}: {str(e)}")

                    instance.vector_store = Chroma(
                        persist_directory=load_path,
                        embedding_function=embeddings
                    )
                    logger.info(f"Loaded local Chroma vector store from {load_path}")
            else:
                raise ValueError(f"Unsupported vector store type: {store_type}")

            return instance
        except Exception as e:
            logger.error(f"Error loading vector store: {str(e)}")
            raise
